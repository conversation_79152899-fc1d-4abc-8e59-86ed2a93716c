# Part1 - CDD 营销内容


# Part2 - TPF 技术验证
1. 魔云腾SuperSDK 跑通
   SuperSDK-解决多台云手机的创建和管理。
2. 魔云腾 RPASDK 跑通
   RPASDK - 解决单台云手机内APP具体的操作
3. Clash 二开 跑通
  可以远程设置链式代理,从而及时更新设备IP

# Part3 -- 产品原型
1. 批量注册后台  - 类TK云大师



# 8.1 Todo
1. 魔云腾固件升级     ✅
2. 魔云腾SDK和RPASDK  ✅
3. 手动配置链式代理

# 8.2 Todo
1. 思考CDD内容思路
2. 思考 Google Sites做 魔法的CPS?

# 8.5 Todo
1. SEO站群的思考  ✅

# 8.6 Todo
1. 思考号群和站群  ✅
2. 确定8月CDD内容列表 ✅
3. 手动配置链式代理  ✅

# 8.7 Todo
1. 买谷歌账号登录测试 可行   ✅
2. 海外APP 授权注册  可行   ✅
3. 思考Gmail账号问题 买OR自己注册   ✅
   魔云腾镜像是无法无手机号验证码注册GmaiL
4. 谷歌企业邮箱 注册TK Gmail 测试   ✅

# 8.8 Todo
1. 了解YoutubeShorts 变现逻辑 ✅
2. 思考号方向的生意   
    判断 1: 注册账部分产品化不是个好决策
           -1 注册号的资源是个难点
           -2 风控是个大问题
           
         2: 号+工具看起来更适合产品化。

    判断  出海云真机是真实手机
             把真机换为魔云腾思考？
   
    判断  不管是做号的批量注册
          还是管理账号   clash/neko 的二开是个必选项
    
# 8.9 Todo
1. 中高端货盘测试小红书  
   海宁等ODM公司有大量最新的款
   这些款+内容是根本竞争力        ✅
2. 草Agent产品化  
   工厂做电商平台的图片拍摄需求    ✅

# 8.10 Todo
1. 出海云真机产品思考
   -1 用户反馈测，有1个准用户需求
      判断是流量池太小，触达到有需求的用户有限
   -2 引流产品
      TK+  对手机的语言和SIM卡Code和IP 综合判断 来识别手机环境问题，以及对应修改。
      这个就可以吸引TK小B，纯引流
   -3 产品化
      基于nekobox/clash的二开部分是确定性需要开发的

2. 思考号+工具产品
   -1 基于Youtube 的产品
      产品侧是 YouTube号+后台
      流量侧是 YouTubeshorts教程内容
   
   -2 基于TikTok 的产品
       TikToK 号+后台  1个号月150元

3. 思考 号+数据产品  ✅
   Reddit广告数据平台
   有官方的广告库API

4. 魔云腾号的情况  ✅
   -FB INS reddit 运行良好
   -谷歌企业邮箱 TK封号  个人邮箱 正常

# 8.11 Todo
1. 反向代购生意 AcBuy 调研  ✅
   AcBuy月实收5千万  毛利40%  
   欧洲市场  快递8元/单
   流量：推测为Reddit 和 KOL
   -1 AcBuy调研  ✅
   -2 aliprice 插件代购生意调研
   https://agent.aliprice.com/dropship/guide.html?type=2

   -3 反向海淘生意调研 ✅
   结论就是复刻代购生意 

2. Dropshiping生意调研  ✅
   teemdrop 
   货1688 淘宝
   日500单
   流量：推测为google 关键词和FB信息流
   实际全是KOL
   

3. Reddit调研  ✅
    调研Reddit 营销核心逻辑
    按照贴吧逻辑来理解  
    发帖-自己老号 OR找KOL
    评论- 截流


# 8.12 Todo

1. 路由器多wifi 多IP方案  ✅
 路由器上openwrt 上开发插件 控制openclash    
 sass后台和插件通信控制openclash    
 配置链式代理信息 
 1个路由器可以发射10-30个wifi。
 sass可以绑定多个路由器。
 最大优点是: 不需要在手机上运行代理APP,提高安全性。

2. S5封装两极跳   ✅
魔云腾支持外置sockt5。
在一台公网服务器上做链式代理 让封装为对外sockt5。
结论: 技术上完全可行。
      但是否有必要,在魔云腾上装clash,链式代理同样可以控制。
   

3. 算账 1000个海外号成本 ✅
   1.号的成本-无需手机号
   gmail成本2元 可以搞定
   gmail youtube 
   Tiktok lemon8
   Fb Ins  
   Discord
   
   2.IP成本

   IP代理商成本

   动态IP 算流量费用贵 只适合注册新账号
   养号场景用静态IP更便宜。
   基本在30-40元/月。
   1IP带5设备成本   6-8元/月/设备
   1IP带10设备成本  3-4元/月/设备

   自建VPS成本
   特价VPS可以做到1IP 10元/月
   1IP带5设备成本   2元/月/设备
    
   
4. https://tikhub.io/ 调研 ✅
   结论见   \Thinking\API.md

5. 群组营销 reddit & FBGroup 待梳理
    方法论:https://mp.weixin.qq.com/s/1IwRnYedBlPc57aAo9Geng


# 8.13 Todo

1. 1000台手机 5000+ 海外号  ✅
   号成本2000   /一次性
   ARM成本2000  /一次性
   IP成本2000+ /每月
   
   如何变现


## 资源测变现
  -1 SIM卡,代理IP,VPS的CPS
  -2 卖号
  -3 刷粉
  
  
## 业务变现
-1 爬数据 API生意
-2 CPS变现
    --Affiliate 联盟
    -- TK等带货

## 工具侧变现 
  -1 出海云真机SAAS  
  -2 号+内容 SAAS（ARM方案）
      月150元 FB INS TK Lemon8 Reddit
      

2. 梳理 API生意 ✅
   是否要先做API 再做SASS产品
   结论 TPF 就可以是一个产品。在海外市场成立。


3. 思考
    1.思考不做SAAS生意
      不做 云真机+发内容的产品  卖工具
      做 号+发内容的产品 卖号+工具

5. 新视角
   草Agent  女装内容生产能力
   变现方式
   1. 号+ 内容 做内容矩阵卖货 TOC
   2. 做工具   TOB SAAS工具
   3. 做供应链  把源头厂家的货,通过草Agent变成电商的链接
               把这些链接上联盟平台 找B分销。
               草Agent 第1变现 给厂家做商拍内容
                       第2变现 做供应链 找流量B分销


# 8.14 Todo 
1. 思考 真机和云机统一方案
   在真机和魔云腾ARM云机安装APK
   核心解决 手机时区  网络 内容发布。
   不做注册方面，用买的gmail账号手工登录。

2. 思考 路由器网络的方案 ✅
   会检测不等于会封号，封号是个多维度的事情
   APK的方案和路由器的方案 用户的使用成本明显APK更低

3. 达人建联 找课程 学习 ✅


# 8.15 Todo 
1. 调研 卖节点的生意  ✅
   软路由+节点
   软路由在500左右
   节点在180元左右/月
   目标用户: 不做矩阵 但要解决IP问题

2. 调研 安卓TK       ✅
    结论是 现在做TK基本还是用iPhone
           安卓因为解决不了SIM卡国家码的问题 没办法用
   这里有机会

# 8.16 Todo 

1. AI种草 服务PPT   ✅
   商拍,种草,AI矩阵素材对应的效果和价格及报价
   商拍,种草   25元/张 一套5张 125元
   AI矩阵素材  5元/张  一套5张 25元
   TOB 给渠道5元费用

2. 思考SAAS生意
   -1 真机方案
   -2 基于ARM的方案
      ARM 一台？元
   SAAS和卖软件是一个生意？
   核心需要判断: 是否需要把产品做出来


# 8.17 Todo  
1. 外行 听过 见过 做过 能用 优秀   ✅
   这个分层模型

   勇敢是真的敢于花成本.而且这个成本,对我正常战略有着巨大影响。
 
2. 梳理现有海外工具模式,市场,定价    ✅
   概念:魔云腾是一个ARM云手机,
   
   ## 截流类的 
   以GTM Social 为代表
   流量:暂无
   核心功能是在Ins,FB获取对标帖子点赞评论用户信息
   获取用户后私信。

   核心技术 RPA
   技术侧有两类 1类是指纹浏览器
               1类是ARM魔云腾
   号：对接号商 成品FB Ins TK 号等
   IP：对接IP
   官方给的用户案例: 独立站卖家,工厂,仿牌等。
   报价: 13800元/年 包含1个魔云腾Q1 2500
         1000个号(端口)
   
   ## 内容发布类
   以TK云大师,TKbound为代表。只做了TK
   流量 月1.6K
   核心功能是多账号发布内容。
    
   核心技术 RPA
   IP：对接IP商
   号：对接邮箱资源 自动注册TK。

   官方给的用户案例: 1. 号商,批量注册账号,卖号，卖橱窗
              2. 内容矩阵
   
   报价 11600一年  不包含魔云腾
   竞品 Tkbound 6,888/年

   这里有个分支
   https://www.upmee.cc
   流量:无数据

   核心技术  飞星对接了TK FB INS 的官方发内的接口
   提供批量发布内容工具
   报价按照账号收费 1号1月15元 3个起
   和TK云大师的区别是 没有注册 登录 的功能
   
   
   ## 私信聚合类
   以SaleSmartly为代表 卖点是私域沟通工具
   公司 广州标品软件有限公司
   流量 月47万
   核心功能是 多个社媒的私信聚合。

   核心技术 官方API 没有的则RPA
   报价: 按照账号收费  1号1月10元  10个起
   官方给的用户案例:百世快递 mimiso 霸王茶姬


 3. 思考改锁区+代理+发布 SAAS

    
    发布用API+RPA两种模式

    核心功能是 1台设备 N个渠道社媒账号
              +内容发布
    一开始就做SAAS模式
    用户转发文章 赠送授权设备数量
    能不能做免费软件的模式？
    我判断是不行
   


   
# 8.18 Todo  
1. 出海工具划分 并思考 

  ## 公域获客类
  以截流,内容矩阵

  ## 私域转化类
  以WhatsApp类工具为代表


# 8.19 Todo  

1. 调研 miroSaas 出海站

keyword clothing ai

## https://uwear.ai/

越南公司
## https://fitroom.app/  
   主要功能:衣服上身  
   月访问量80万
   价格: 56元 200积分 （换一次1积分）

   背后公司 https://silverai.com/
   
   https://snapedit.app/
   主要功能:照片编辑
   月访问量 350万
    
   https://snapbg.ai/ 
   主要功能:移除背景 
   
   价格: 140元 100点 （1张1.4元）

   https://passportmaker.com/
   主要功能:AI生成护照头像
   月访问量3万
    
   https://betterimage.ai/
   主要功能:图片放大
   月访问量50万

## 矩阵这样的产品
https://tikmatrix.com/  TK矩阵
月访问量4万
报价  210元/月  5台手机
     420元/月   20台手机
https://tikmatrix.com/VideoMagic 
https://tikmatrix.com/IgMatrix  insgram

https://user.tikmatrix.com/#google_vignette


# 8.20 Todo  

1. 调研出海站 -AI工具

结论:
## 1 只解决一个高意图的特定问题 (Specificity)
snapbg.ai 的流量来自“remove bg”（月搜索量394万）和“remove background ai”。
snapedit.app 的核心流量来自俄语的“улучшить качество фото”（提升照片质量）和越南语的“làm nét ảnh”（让图片变清晰）。
fitroom.app 瞄准的是“ai clothes changer”（AI换衣）。
## 2. 全球化，
snapedit.app: 俄罗斯、越南、印尼、印度
snapbg.ai: 越南、乌克兰、印尼、俄罗斯
passportmaker.com: 俄罗斯、美国、肯尼亚、尼日利亚

## 3 全球搜索引擎优化：
snapedit.app: 65.58% 来自搜索
passportmaker.com: 54.32% 来自搜索
betterimage.ai: 50.77% 来自搜索
fitroom.app: 52.17% 来自搜索



2. 调研出海站 -营销类 

## 刷粉类
https://www.trollishly.com/
功能:TK youtube 刷粉 刷点赞
报价 93 1000粉
流量: 40万/月
https://views4you.com/
流量: 150万/月
https://popularitybazaar.com/
流量: 18万/月
https://www.socialwick.com/tiktok/followers
流量: 59万/月

https://www.igtaken.com/  谷歌广告


## 分析类
https://exolyt.com/
Tiktok 账户监控分析


## 计数类
tiktok counter  似乎是事实统计某个用户的粉丝增长情况？
https://tokcounter.com/
https://tokcount.com/

TikTok Earning Calculator  TikTok 用户收入预估
https://tikcalculator.com/


https://livecounts.io/tiktok-live-follower-counter
直播粉丝计数器

## 数据API
https://ensembledata.com/
https://tikhub.io/
https://www.ayrshare.com
https://tikapi.io/

## 基于广告库的产品
https://www.foreplay.co/
https://tryatria.com/

## TikTok SPY
https://www.pipiads.com/
https://www.adwins.net/
https://adspyder.io/

## 矩阵工具
https://tikmatrix.com/
https://somiibo.com/  自动关注


## SAAS
https://www.dashsocial.com/
多平台社媒管理？
https://www.hootsuite.com/


## 视频下载
https://ssstik.io  
流量: 1.2亿/月
https://tiktokio.com/
流量: 230万/月

https://downsub.com/
下载视频字幕
流量: 280万/月
字幕站给以下导流
https://www.aiarty.com/  AI图片站 60万/月
https://www.taboola.com/ 广告联盟 3个位置





2. 调研 出海卖货站
结论:
1. 和工具站一样  一个站主做一部分细分的货
2. 不一样的是 工具服务类的 SEO可行
             卖货消费类的 适合信息流广告




# 8.21 Todo  

1. 多平台关键词研究工具 调研 ✅
https://answerthepublic.com/ 多平台关键词研究


2. 调研 亚马逊 coupon 等 ✅



# 8.22 Todo  
1. 系统调研 谷歌套利

2. 思考下载类引流 -导流到AI工具站的路径

3. 系统调研 国外广告联盟 自刷

4. 思考号群和站群的相互迭代
